# Document Number Fields Implementation Test

## Summary
This document outlines the implementation of document number fields for third-party identification in the BigCapital system.

## Changes Made

### 1. Database Migration
- **File**: `packages/server/src/database/migrations/20250102000000_add_document_fields_to_contacts_table.ts`
- **Changes**: Added `document_number` and `document_type` fields to the `contacts` table
- **Fields**:
  - `document_number`: VARCHAR, nullable - stores the actual ID number (NIT, Cédula, etc.)
  - `document_type`: VARCHAR, nullable - specifies the type of document for categorization

### 2. Backend Models and Types

#### Contact Model
- **File**: `packages/server/src/modules/Contacts/models/Contact.ts`
- **Changes**: Added `documentNumber?: string` and `documentType?: string` properties

#### Contact Types
- **File**: `packages/server/src/modules/Contacts/types/Contacts.types.ts`
- **Changes**: Updated `IContact`, `IContactNewDTO`, and `IContactEditDTO` interfaces

#### Customer Model and Types
- **File**: `packages/server/src/modules/Customers/models/Customer.ts`
- **Changes**: Added document fields to Customer model
- **File**: `packages/server/src/modules/Customers/types/Customers.types.ts`
- **Changes**: Updated customer interfaces

#### Vendor Model and Types
- **File**: `packages/server/src/modules/Vendors/models/Vendor.ts`
- **Changes**: Added document fields to Vendor model
- **File**: `packages/server/src/modules/Vendors/types/Vendors.types.ts`
- **Changes**: Updated vendor interfaces

### 3. API DTOs and Validation

#### Customer DTOs
- **File**: `packages/server/src/modules/Customers/dtos/CreateCustomer.dto.ts`
- **Changes**: Added document_number and document_type fields with validation
- **File**: `packages/server/src/modules/Customers/dtos/EditCustomer.dto.ts`
- **Changes**: Added document fields for editing

#### Vendor DTOs
- **File**: `packages/server/src/modules/Vendors/dtos/CreateVendor.dto.ts`
- **Changes**: Added document_number and document_type fields with validation
- **File**: `packages/server/src/modules/Vendors/dtos/EditVendor.dto.ts`
- **Changes**: Added document fields for editing

### 4. Frontend Forms and Validation

#### Customer Form Schema
- **File**: `packages/webapp/src/containers/Customers/CustomerForm/CustomerForm.schema.tsx`
- **Changes**: Added Yup validation for document_number and document_type fields

#### Customer Form Utils
- **File**: `packages/webapp/src/containers/Customers/CustomerForm/utils.tsx`
- **Changes**: Added document fields to defaultInitialValues

#### Vendor Form Schema
- **File**: `packages/webapp/src/containers/Vendors/VendorForm/VendorForm.schema.tsx`
- **Changes**: Added Yup validation for document_number and document_type fields

#### Vendor Form Utils
- **File**: `packages/webapp/src/containers/Vendors/VendorForm/utils.tsx`
- **Changes**: Added document fields to defaultInitialValues

## Document Types Supported

The implementation supports various document types commonly used in Latin American countries:

- **NIT** (Número de Identificación Tributaria) - Tax ID Number
- **Cedula** (Cédula de Ciudadanía) - National ID
- **Cedula_Extranjeria** (Cédula de Extranjería) - Foreign ID
- **Other** - For any other official document numbers

## Usage Examples

### Creating a Customer with Document Number
```json
{
  "customer_type": "business",
  "display_name": "Acme Corporation",
  "document_number": "*********-7",
  "document_type": "NIT",
  "email": "<EMAIL>"
}
```

### Creating a Vendor with Document Number
```json
{
  "display_name": "John Doe Services",
  "document_number": "12345678",
  "document_type": "Cedula",
  "email": "<EMAIL>"
}
```

## Testing Checklist

- [ ] Database migration runs successfully
- [ ] Customer creation with document fields works
- [ ] Customer editing with document fields works
- [ ] Vendor creation with document fields works
- [ ] Vendor editing with document fields works
- [ ] Frontend forms display document fields
- [ ] Frontend validation works for document fields
- [ ] API endpoints accept and return document fields
- [ ] Document fields are properly stored and retrieved

## Next Steps

1. Run database migration: `node packages/server/build/commands.js tenants:migrate:latest`
2. Test customer and vendor creation/editing through API
3. Test frontend forms for document field input
4. Add any additional validation rules if needed
5. Update documentation for API endpoints

## Notes

- Document fields are optional (nullable) to maintain backward compatibility
- The implementation follows the existing patterns in the codebase
- All changes are non-breaking and maintain existing functionality
- The document_type field allows for future extensibility to support additional document types
