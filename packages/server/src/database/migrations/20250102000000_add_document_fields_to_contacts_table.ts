/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
exports.up = function (knex) {
  return knex.schema.alterTable('contacts', (table) => {
    table.string('document_number').nullable().after('display_name');
    table.string('document_type').nullable().after('document_number');
  });
};

/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
exports.down = function (knex) {
  return knex.schema.alterTable('contacts', (table) => {
    table.dropColumn('document_number');
    table.dropColumn('document_type');
  });
};
