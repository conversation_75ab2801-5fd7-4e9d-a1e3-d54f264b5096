export const TrialBalanceSheetResponseExample = {
  data: {
    accounts: [
      {
        id: 1000,
        parent_account_id: null,
        name: 'Bank Account',
        formatted_name: 'Bank Account - 10001',
        code: '10001',
        account_normal: 'debit',
        credit: 500,
        debit: 0,
        balance: -500,
        currency_code: 'USD',
        formatted_credit: '500.00',
        formatted_debit: '',
        formatted_balance: '-500.00',
        children: [],
      },
      {
        id: 1001,
        parent_account_id: null,
        name: 'Saving Bank Account',
        formatted_name: 'Saving Bank Account - 10002',
        code: '10002',
        account_normal: 'debit',
        credit: 0,
        debit: 0,
        balance: 0,
        currency_code: 'USD',
        formatted_credit: '',
        formatted_debit: '',
        formatted_balance: '',
        children: [],
      },
      {
        id: 1002,
        parent_account_id: null,
        name: 'Undeposited Funds',
        formatted_name: 'Undeposited Funds - 10003',
        code: '10003',
        account_normal: 'debit',
        credit: 0,
        debit: 0,
        balance: 0,
        currency_code: 'USD',
        formatted_credit: '',
        formatted_debit: '',
        formatted_balance: '',
        children: [],
      },
      {
        id: 1003,
        parent_account_id: null,
        name: 'Petty Cash',
        formatted_name: 'Petty Cash - 10004',
        code: '10004',
        account_normal: 'debit',
        credit: 0,
        debit: 0,
        balance: 0,
        currency_code: 'USD',
        formatted_credit: '',
        formatted_debit: '',
        formatted_balance: '',
        children: [],
      },
      {
        id: 1004,
        parent_account_id: null,
        name: 'Computer Equipment',
        formatted_name: 'Computer Equipment - 10005',
        code: '10005',
        account_normal: 'debit',
        credit: 0,
        debit: 0,
        balance: 0,
        currency_code: 'USD',
        formatted_credit: '',
        formatted_debit: '',
        formatted_balance: '',
        children: [],
      },
      {
        id: 1005,
        parent_account_id: null,
        name: 'Office Equipment',
        formatted_name: 'Office Equipment - 10006',
        code: '10006',
        account_normal: 'debit',
        credit: 0,
        debit: 0,
        balance: 0,
        currency_code: 'USD',
        formatted_credit: '',
        formatted_debit: '',
        formatted_balance: '',
        children: [],
      },
      {
        id: 1006,
        parent_account_id: null,
        name: 'Accounts Receivable (A/R)',
        formatted_name: 'Accounts Receivable (A/R) - 10007',
        code: '10007',
        account_normal: 'debit',
        credit: 0,
        debit: 0,
        balance: 0,
        currency_code: 'USD',
        formatted_credit: '',
        formatted_debit: '',
        formatted_balance: '',
        children: [],
      },
      {
        id: 1007,
        parent_account_id: null,
        name: 'Inventory Asset',
        formatted_name: 'Inventory Asset - 10008',
        code: '10008',
        account_normal: 'debit',
        credit: 0,
        debit: 1000,
        balance: 1000,
        currency_code: 'USD',
        formatted_credit: '',
        formatted_debit: '1,000.00',
        formatted_balance: '1,000.00',
        children: [],
      },
      {
        id: 1008,
        parent_account_id: null,
        name: 'Accounts Payable (A/P)',
        formatted_name: 'Accounts Payable (A/P) - 20001',
        code: '20001',
        account_normal: 'credit',
        credit: 2000,
        debit: 700,
        balance: -1300,
        currency_code: 'USD',
        formatted_credit: '2,000.00',
        formatted_debit: '700.00',
        formatted_balance: '-1,300.00',
        children: [],
      },
      {
        id: 1009,
        parent_account_id: null,
        name: 'Owner A Drawings',
        formatted_name: 'Owner A Drawings - 20002',
        code: '20002',
        account_normal: 'credit',
        credit: 0,
        debit: 0,
        balance: 0,
        currency_code: 'USD',
        formatted_credit: '',
        formatted_debit: '',
        formatted_balance: '',
        children: [],
      },
      {
        id: 1010,
        parent_account_id: null,
        name: 'Loan',
        formatted_name: 'Loan - 20003',
        code: '20003',
        account_normal: 'credit',
        credit: 0,
        debit: 10000,
        balance: 10000,
        currency_code: 'USD',
        formatted_credit: '',
        formatted_debit: '10,000.00',
        formatted_balance: '10,000.00',
        children: [],
      },
      {
        id: 1011,
        parent_account_id: null,
        name: 'Opening Balance Liabilities',
        formatted_name: 'Opening Balance Liabilities - 20004',
        code: '20004',
        account_normal: 'credit',
        credit: 0,
        debit: 0,
        balance: 0,
        currency_code: 'USD',
        formatted_credit: '',
        formatted_debit: '',
        formatted_balance: '',
        children: [],
      },
      {
        id: 1012,
        parent_account_id: null,
        name: 'Revenue Received in Advance',
        formatted_name: 'Revenue Received in Advance - 20005',
        code: '20005',
        account_normal: 'credit',
        credit: 0,
        debit: 0,
        balance: 0,
        currency_code: 'USD',
        formatted_credit: '',
        formatted_debit: '',
        formatted_balance: '',
        children: [],
      },
      {
        id: 1013,
        parent_account_id: null,
        name: 'Tax Payable',
        formatted_name: 'Tax Payable - 20006',
        code: '20006',
        account_normal: 'credit',
        credit: 0,
        debit: 0,
        balance: 0,
        currency_code: 'USD',
        formatted_credit: '',
        formatted_debit: '',
        formatted_balance: '',
        children: [],
      },
      {
        id: 1014,
        parent_account_id: null,
        name: 'Retained Earnings',
        formatted_name: 'Retained Earnings - 30001',
        code: '30001',
        account_normal: 'credit',
        credit: 0,
        debit: 0,
        balance: 0,
        currency_code: 'USD',
        formatted_credit: '',
        formatted_debit: '',
        formatted_balance: '',
        children: [],
      },
      {
        id: 1015,
        parent_account_id: null,
        name: 'Opening Balance Equity',
        formatted_name: 'Opening Balance Equity - 30002',
        code: '30002',
        account_normal: 'credit',
        credit: 0,
        debit: 0,
        balance: 0,
        currency_code: 'USD',
        formatted_credit: '',
        formatted_debit: '',
        formatted_balance: '',
        children: [],
      },
      {
        id: 1016,
        parent_account_id: null,
        name: "Owner's Equity",
        formatted_name: "Owner's Equity - 30003",
        code: '30003',
        account_normal: 'credit',
        credit: 0,
        debit: 0,
        balance: 0,
        currency_code: 'USD',
        formatted_credit: '',
        formatted_debit: '',
        formatted_balance: '',
        children: [],
      },
      {
        id: 1017,
        parent_account_id: null,
        name: 'Drawings',
        formatted_name: 'Drawings - 30003',
        code: '30003',
        account_normal: 'credit',
        credit: 0,
        debit: 0,
        balance: 0,
        currency_code: 'USD',
        formatted_credit: '',
        formatted_debit: '',
        formatted_balance: '',
        children: [],
      },
      {
        id: 1018,
        parent_account_id: null,
        name: 'Other Expenses',
        formatted_name: 'Other Expenses - 40011',
        code: '40011',
        account_normal: 'debit',
        credit: 0,
        debit: 0,
        balance: 0,
        currency_code: 'USD',
        formatted_credit: '',
        formatted_debit: '',
        formatted_balance: '',
        children: [],
      },
      {
        id: 1019,
        parent_account_id: null,
        name: 'Cost of Goods Sold',
        formatted_name: 'Cost of Goods Sold - 40002',
        code: '40002',
        account_normal: 'debit',
        credit: 200,
        debit: 1000,
        balance: 800,
        currency_code: 'USD',
        formatted_credit: '200.00',
        formatted_debit: '1,000.00',
        formatted_balance: '800.00',
        children: [],
      },
      {
        id: 1020,
        parent_account_id: null,
        name: 'Office expenses',
        formatted_name: 'Office expenses - 40003',
        code: '40003',
        account_normal: 'debit',
        credit: 0,
        debit: 0,
        balance: 0,
        currency_code: 'USD',
        formatted_credit: '',
        formatted_debit: '',
        formatted_balance: '',
        children: [],
      },
      {
        id: 1021,
        parent_account_id: null,
        name: 'Rent',
        formatted_name: 'Rent - 40004',
        code: '40004',
        account_normal: 'debit',
        credit: 0,
        debit: 0,
        balance: 0,
        currency_code: 'USD',
        formatted_credit: '',
        formatted_debit: '',
        formatted_balance: '',
        children: [],
      },
      {
        id: 1022,
        parent_account_id: null,
        name: 'Exchange Gain or Loss',
        formatted_name: 'Exchange Gain or Loss - 40005',
        code: '40005',
        account_normal: 'debit',
        credit: 0,
        debit: 123123,
        balance: 123123,
        currency_code: 'USD',
        formatted_credit: '',
        formatted_debit: '123,123.00',
        formatted_balance: '123,123.00',
        children: [],
      },
      {
        id: 1023,
        parent_account_id: null,
        name: 'Bank Fees and Charges',
        formatted_name: 'Bank Fees and Charges - 40006',
        code: '40006',
        account_normal: 'debit',
        credit: 0,
        debit: 0,
        balance: 0,
        currency_code: 'USD',
        formatted_credit: '',
        formatted_debit: '',
        formatted_balance: '',
        children: [],
      },
      {
        id: 1024,
        parent_account_id: null,
        name: 'Depreciation Expense',
        formatted_name: 'Depreciation Expense - 40007',
        code: '40007',
        account_normal: 'debit',
        credit: 10000,
        debit: 0,
        balance: -10000,
        currency_code: 'USD',
        formatted_credit: '10,000.00',
        formatted_debit: '',
        formatted_balance: '-10,000.00',
        children: [],
      },
      {
        id: 1025,
        parent_account_id: null,
        name: 'Sales of Product Income',
        formatted_name: 'Sales of Product Income - 50001',
        code: '50001',
        account_normal: 'credit',
        credit: 0,
        debit: 0,
        balance: 0,
        currency_code: 'USD',
        formatted_credit: '',
        formatted_debit: '',
        formatted_balance: '',
        children: [],
      },
      {
        id: 1026,
        parent_account_id: null,
        name: 'Sales of Service Income',
        formatted_name: 'Sales of Service Income - 50002',
        code: '50002',
        account_normal: 'credit',
        credit: 0,
        debit: 0,
        balance: 0,
        currency_code: 'USD',
        formatted_credit: '',
        formatted_debit: '',
        formatted_balance: '',
        children: [],
      },
      {
        id: 1027,
        parent_account_id: null,
        name: 'Uncategorized Income',
        formatted_name: 'Uncategorized Income - 50003',
        code: '50003',
        account_normal: 'credit',
        credit: 0,
        debit: 0,
        balance: 0,
        currency_code: 'USD',
        formatted_credit: '',
        formatted_debit: '',
        formatted_balance: '',
        children: [],
      },
      {
        id: 1029,
        parent_account_id: null,
        name: 'Unearned Revenue',
        formatted_name: 'Unearned Revenue - 50005',
        code: '50005',
        account_normal: 'credit',
        credit: 0,
        debit: 0,
        balance: 0,
        currency_code: 'USD',
        formatted_credit: '',
        formatted_debit: '',
        formatted_balance: '',
        children: [],
      },
      {
        id: 1030,
        parent_account_id: null,
        name: 'Prepaid Expenses',
        formatted_name: 'Prepaid Expenses - 100010',
        code: '100010',
        account_normal: 'debit',
        credit: 123123,
        debit: 0,
        balance: -123123,
        currency_code: 'USD',
        formatted_credit: '123,123.00',
        formatted_debit: '',
        formatted_balance: '-123,123.00',
        children: [],
      },
      {
        id: 1031,
        parent_account_id: null,
        name: 'Discount',
        formatted_name: 'Discount - 40008',
        code: '40008',
        account_normal: 'credit',
        credit: 0,
        debit: 0,
        balance: 0,
        currency_code: 'USD',
        formatted_credit: '',
        formatted_debit: '',
        formatted_balance: '',
        children: [],
      },
      {
        id: 1032,
        parent_account_id: null,
        name: 'Purchase Discount',
        formatted_name: 'Purchase Discount - 40009',
        code: '40009',
        account_normal: 'debit',
        credit: 0,
        debit: 0,
        balance: 0,
        currency_code: 'USD',
        formatted_credit: '',
        formatted_debit: '',
        formatted_balance: '',
        children: [],
      },
      {
        id: 1033,
        parent_account_id: null,
        name: 'Other Charges',
        formatted_name: 'Other Charges - 40010',
        code: '40010',
        account_normal: 'credit',
        credit: 0,
        debit: 0,
        balance: 0,
        currency_code: 'USD',
        formatted_credit: '',
        formatted_debit: '',
        formatted_balance: '',
        children: [],
      },
      {
        id: 1034,
        parent_account_id: null,
        name: 'Chase - Plaid Checking',
        formatted_name: 'Chase - Plaid Checking',
        code: '',
        account_normal: 'debit',
        credit: 0,
        debit: 0,
        balance: 0,
        currency_code: 'USD',
        formatted_credit: '',
        formatted_debit: '',
        formatted_balance: '',
        children: [],
      },
    ],
    total: {
      credit: 135823,
      debit: 135823,
      balance: 0,
      currency_code: 'USD',
      formatted_credit: '$135,823.00',
      formatted_debit: '$135,823.00',
      formatted_balance: '$0.00',
    },
  },
  query: {
    from_date: '2025-01-01',
    to_date: '2025-06-16',
    number_format: {
      divide_on1000: false,
      negative_format: 'mines',
      show_zero: false,
      format_money: 'total',
      precision: 2,
    },
    basis: 'accrual',
    none_zero: false,
    none_transactions: true,
    only_active: false,
    account_ids: [null],
  },
  meta: {
    organization_name: 'BIGCAPITAL, INC',
    base_currency: 'USD',
    date_format: 'DD MMM yyyy',
    is_cost_compute_running: false,
    sheet_name: 'Trial Balance Sheet',
    formatted_from_date: '2025/01/01',
    formatted_to_date: '2025/06/16',
    formatted_date_range: 'From 2025/01/01 to 2025/06/16',
  },
};
